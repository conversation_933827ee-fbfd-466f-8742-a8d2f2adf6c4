<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار سلة الخدمات المحسنة</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .test-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .test-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .test-header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .test-header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .test-content {
            padding: 30px;
        }

        .test-section {
            margin-bottom: 40px;
            padding: 25px;
            background: #f8f9fa;
            border-radius: 15px;
            border-left: 5px solid #667eea;
        }

        .test-section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .control-buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }

        .test-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        .test-btn.secondary {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .test-btn.warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        }

        .test-btn.danger {
            background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
        }

        .services-demo {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .demo-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 20px;
        }

        .demo-column {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
        }

        .demo-column h3 {
            color: #495057;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #dee2e6;
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
            margin: 5px 0;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
        }

        .status-warning {
            background: #fff3cd;
            color: #856404;
        }

        .status-info {
            background: #d1ecf1;
            color: #0c5460;
        }

        .log-container {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 15px;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 3px 0;
        }

        .log-entry.success {
            color: #2ecc71;
        }

        .log-entry.error {
            color: #e74c3c;
        }

        .log-entry.warning {
            color: #f39c12;
        }

        .log-entry.info {
            color: #3498db;
        }

        /* Include enhanced services styles */
        .services-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f8f9fa;
        }

        .services-controls {
            display: flex;
            gap: 8px;
        }

        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
        }

        .btn-sm {
            padding: 6px 10px;
            font-size: 0.8rem;
        }

        .btn-info {
            background: #17a2b8;
        }

        .btn-secondary {
            background: #6c757d;
        }

        .btn-primary {
            background: #007bff;
        }

        .services-filter-info {
            margin-bottom: 15px;
        }

        .filter-badge {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            color: #1565c0;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .clear-filter {
            background: rgba(255, 255, 255, 0.3);
            border: none;
            color: #1565c0;
            padding: 4px 6px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .services-container {
            max-height: 600px;
            overflow-y: auto;
            padding: 10px 0;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 15px;
            padding: 10px;
        }

        .no-doctor-selected {
            text-align: center;
            padding: 40px 20px;
            color: #6c757d;
        }

        @media (max-width: 768px) {
            .demo-grid {
                grid-template-columns: 1fr;
            }

            .control-buttons {
                justify-content: center;
            }

            .test-btn {
                flex: 1;
                min-width: 200px;
            }

            .services-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-flask"></i> اختبار سلة الخدمات المحسنة</h1>
            <p>اختبار شامل لنظام عرض وفلترة الخدمات الطبية</p>
        </div>

        <div class="test-content">
            <!-- Test Controls -->
            <div class="test-section">
                <h2><i class="fas fa-cogs"></i> أدوات التحكم في الاختبار</h2>
                
                <div class="control-buttons">
                    <button class="test-btn" onclick="testShowAllServices()">
                        <i class="fas fa-list"></i>
                        عرض جميع الخدمات
                    </button>
                    
                    <button class="test-btn secondary" onclick="testFilterBySpecialty()">
                        <i class="fas fa-filter"></i>
                        فلترة حسب التخصص
                    </button>
                    
                    <button class="test-btn warning" onclick="testFilterByDoctor()">
                        <i class="fas fa-user-md"></i>
                        فلترة حسب الطبيب
                    </button>
                    
                    <button class="test-btn danger" onclick="testClearFilters()">
                        <i class="fas fa-times"></i>
                        إلغاء الفلاتر
                    </button>
                </div>

                <div class="control-buttons">
                    <button class="test-btn" onclick="testAddToCart()">
                        <i class="fas fa-cart-plus"></i>
                        إضافة خدمة للسلة
                    </button>
                    
                    <button class="test-btn secondary" onclick="testRefreshServices()">
                        <i class="fas fa-sync-alt"></i>
                        إعادة تحميل الخدمات
                    </button>
                    
                    <button class="test-btn warning" onclick="clearTestLog()">
                        <i class="fas fa-eraser"></i>
                        مسح سجل الاختبار
                    </button>
                </div>
            </div>

            <!-- Services Demo -->
            <div class="test-section">
                <h2><i class="fas fa-stethoscope"></i> عرض الخدمات</h2>
                
                <div class="services-demo">
                    <div class="services-header">
                        <h3><i class="fas fa-stethoscope"></i> خدمات الطبيب</h3>
                        <div class="services-controls">
                            <button class="btn btn-sm btn-info" onclick="showAllServices()" title="عرض جميع الخدمات">
                                <i class="fas fa-list"></i>
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="refreshServices()" title="إعادة تحميل">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="services-filter-info" id="servicesFilterInfo" style="display: none;">
                        <div class="filter-badge">
                            <i class="fas fa-filter"></i>
                            <span id="filterText">جميع الخدمات</span>
                            <button class="clear-filter" onclick="clearServicesFilter()" title="إلغاء الفلتر">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div id="doctorServicesContainer" class="services-container">
                        <div class="no-doctor-selected">
                            <i class="fas fa-user-md" style="font-size: 3rem; opacity: 0.3; margin-bottom: 15px;"></i>
                            <p>يرجى اختيار طبيب لعرض خدماته</p>
                            <button class="btn btn-primary" onclick="showAllServices()">
                                <i class="fas fa-eye"></i>
                                عرض جميع الخدمات المتاحة
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Test Results -->
            <div class="test-section">
                <h2><i class="fas fa-chart-line"></i> نتائج الاختبار</h2>
                
                <div class="demo-grid">
                    <div class="demo-column">
                        <h3><i class="fas fa-info-circle"></i> حالة النظام</h3>
                        <div id="systemStatus">
                            <div class="status-indicator status-info">
                                <i class="fas fa-clock"></i>
                                في انتظار بدء الاختبار
                            </div>
                        </div>
                    </div>
                    
                    <div class="demo-column">
                        <h3><i class="fas fa-shopping-cart"></i> حالة السلة</h3>
                        <div id="cartStatus">
                            <div class="status-indicator status-warning">
                                <i class="fas fa-shopping-cart"></i>
                                السلة فارغة
                            </div>
                        </div>
                    </div>
                </div>

                <div class="log-container" id="testLog">
                    <div class="log-entry info">
                        <strong>[INFO]</strong> نظام اختبار سلة الخدمات المحسنة جاهز للاستخدام
                    </div>
                    <div class="log-entry info">
                        <strong>[INFO]</strong> استخدم الأزرار أعلاه لاختبار الوظائف المختلفة
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Include required scripts -->
    <script src="enhanced-services.js"></script>
    <script>
        // Test logging system
        function logTest(message, type = 'info') {
            const logContainer = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString('ar-EG');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function clearTestLog() {
            document.getElementById('testLog').innerHTML = '';
            logTest('تم مسح سجل الاختبار', 'info');
        }

        function updateSystemStatus(status, type = 'info') {
            const statusContainer = document.getElementById('systemStatus');
            const icons = {
                info: 'fas fa-info-circle',
                success: 'fas fa-check-circle',
                warning: 'fas fa-exclamation-triangle',
                error: 'fas fa-times-circle'
            };
            
            statusContainer.innerHTML = `
                <div class="status-indicator status-${type}">
                    <i class="${icons[type]}"></i>
                    ${status}
                </div>
            `;
        }

        function updateCartStatus(status, type = 'warning') {
            const cartContainer = document.getElementById('cartStatus');
            const icons = {
                info: 'fas fa-shopping-cart',
                success: 'fas fa-check-circle',
                warning: 'fas fa-exclamation-triangle',
                error: 'fas fa-times-circle'
            };
            
            cartContainer.innerHTML = `
                <div class="status-indicator status-${type}">
                    <i class="${icons[type]}"></i>
                    ${status}
                </div>
            `;
        }

        // Test functions
        function testShowAllServices() {
            logTest('اختبار عرض جميع الخدمات...', 'info');
            updateSystemStatus('جاري عرض جميع الخدمات', 'info');
            
            if (window.enhancedServicesManager) {
                window.enhancedServicesManager.showAllServices();
                logTest('✅ تم عرض جميع الخدمات بنجاح', 'success');
                updateSystemStatus('تم عرض جميع الخدمات', 'success');
            } else {
                logTest('❌ نظام الخدمات المحسن غير متاح', 'error');
                updateSystemStatus('خطأ: النظام غير متاح', 'error');
            }
        }

        function testFilterBySpecialty() {
            logTest('اختبار الفلترة حسب التخصص...', 'info');
            updateSystemStatus('جاري فلترة الخدمات حسب التخصص', 'info');
            
            if (window.enhancedServicesManager) {
                // Test with internal medicine specialty
                const specialtyData = { id: 'internal', name: 'الباطنة العامة' };
                window.enhancedServicesManager.handleSpecialtySelection(specialtyData);
                logTest('✅ تم فلترة الخدمات حسب تخصص الباطنة العامة', 'success');
                updateSystemStatus('تم فلترة الخدمات حسب التخصص', 'success');
            } else {
                logTest('❌ نظام الخدمات المحسن غير متاح', 'error');
                updateSystemStatus('خطأ: النظام غير متاح', 'error');
            }
        }

        function testFilterByDoctor() {
            logTest('اختبار الفلترة حسب الطبيب...', 'info');
            updateSystemStatus('جاري فلترة الخدمات حسب الطبيب', 'info');
            
            if (window.enhancedServicesManager) {
                // Test with a sample doctor
                const doctorData = { id: 'doc_001', name: 'د. أحمد محمد', specialty: 'internal' };
                window.enhancedServicesManager.handleDoctorSelection(doctorData);
                logTest('✅ تم فلترة الخدمات حسب د. أحمد محمد', 'success');
                updateSystemStatus('تم فلترة الخدمات حسب الطبيب', 'success');
            } else {
                logTest('❌ نظام الخدمات المحسن غير متاح', 'error');
                updateSystemStatus('خطأ: النظام غير متاح', 'error');
            }
        }

        function testClearFilters() {
            logTest('اختبار إلغاء الفلاتر...', 'info');
            updateSystemStatus('جاري إلغاء جميع الفلاتر', 'info');
            
            if (window.enhancedServicesManager) {
                window.enhancedServicesManager.clearServicesFilter();
                logTest('✅ تم إلغاء جميع الفلاتر', 'success');
                updateSystemStatus('تم إلغاء جميع الفلاتر', 'success');
            } else {
                logTest('❌ نظام الخدمات المحسن غير متاح', 'error');
                updateSystemStatus('خطأ: النظام غير متاح', 'error');
            }
        }

        function testAddToCart() {
            logTest('اختبار إضافة خدمة للسلة...', 'info');
            updateCartStatus('جاري إضافة خدمة للسلة', 'info');
            
            if (window.enhancedServicesManager) {
                // Test adding first service
                window.enhancedServicesManager.addToCart('srv_001');
                logTest('✅ تم إضافة خدمة كشف باطنة عام للسلة', 'success');
                updateCartStatus('تم إضافة خدمة للسلة', 'success');
            } else {
                logTest('❌ نظام الخدمات المحسن غير متاح', 'error');
                updateCartStatus('خطأ: النظام غير متاح', 'error');
            }
        }

        function testRefreshServices() {
            logTest('اختبار إعادة تحميل الخدمات...', 'info');
            updateSystemStatus('جاري إعادة تحميل الخدمات', 'info');
            
            if (window.enhancedServicesManager) {
                window.enhancedServicesManager.refreshServices();
                logTest('✅ تم إعادة تحميل الخدمات بنجاح', 'success');
                updateSystemStatus('تم إعادة تحميل الخدمات', 'success');
            } else {
                logTest('❌ نظام الخدمات المحسن غير متاح', 'error');
                updateSystemStatus('خطأ: النظام غير متاح', 'error');
            }
        }

        // Initialize test page
        document.addEventListener('DOMContentLoaded', function() {
            logTest('تم تحميل صفحة الاختبار بنجاح', 'success');
            
            // Check if enhanced services manager is available
            setTimeout(() => {
                if (window.enhancedServicesManager) {
                    logTest('✅ نظام الخدمات المحسن متاح ومُحمل', 'success');
                    updateSystemStatus('النظام جاهز للاختبار', 'success');
                } else {
                    logTest('⚠️ نظام الخدمات المحسن غير متاح - تأكد من تحميل الملفات المطلوبة', 'warning');
                    updateSystemStatus('النظام غير متاح', 'warning');
                }
            }, 1000);
        });
    </script>
</body>
</html>
